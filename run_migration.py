#!/usr/bin/env python3
"""
Migration Runner Script
Helps set up and run the SQLite to PostgreSQL migration.
"""

import os
import sys
import subprocess

def check_requirements():
    """Check if required packages are installed"""
    try:
        import psycopg2
        print("✓ psycopg2 is installed")
        return True
    except ImportError:
        print("✗ psycopg2 is not installed")
        return False

def install_requirements():
    """Install required packages"""
    print("Installing requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install requirements: {e}")
        return False

def check_database_url():
    """Check if DATABASE_URL is set"""
    db_url = os.getenv("DATABASE_URL")
    if db_url:
        print(f"✓ DATABASE_URL is set: {db_url[:50]}...")
        return True
    else:
        print("✗ DATABASE_URL environment variable is not set")
        print("\nPlease set your DATABASE_URL environment variable:")
        print("Example: export DATABASE_URL='postgresql://username:password@localhost:5432/database_name'")
        print("Or on Windows: set DATABASE_URL=postgresql://username:password@localhost:5432/database_name")
        return False

def check_sqlite_file():
    """Check if SQLite file exists"""
    sqlite_path = "database/db.sqlite"
    if os.path.exists(sqlite_path):
        print(f"✓ SQLite database found: {sqlite_path}")
        return True
    else:
        print(f"✗ SQLite database not found: {sqlite_path}")
        return False

def main():
    print("SQLite to PostgreSQL Migration Setup")
    print("=" * 40)
    
    # Check all prerequisites
    checks_passed = 0
    total_checks = 4
    
    if check_requirements():
        checks_passed += 1
    else:
        if install_requirements():
            checks_passed += 1
    
    if check_database_url():
        checks_passed += 1
    
    if check_sqlite_file():
        checks_passed += 1
    
    # Check if PostgreSQL tables exist (basic connectivity test)
    if checks_passed >= 3:  # Allow running even if some checks fail
        try:
            import psycopg2
            conn = psycopg2.connect(os.getenv("DATABASE_URL"))
            with conn.cursor() as cursor:
                cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'")
                tables = cursor.fetchall()
                if tables:
                    print(f"✓ PostgreSQL connection successful, found {len(tables)} tables")
                    checks_passed += 1
                else:
                    print("⚠ PostgreSQL connected but no tables found (this is expected for a fresh database)")
                    checks_passed += 1
            conn.close()
        except Exception as e:
            print(f"✗ PostgreSQL connection failed: {e}")
    
    print(f"\nChecks passed: {checks_passed}/{total_checks + 1}")
    
    if checks_passed >= 3:
        print("\n" + "=" * 40)
        print("Ready to run migration!")
        print("Run: python migrate_sqlite_to_postgres.py")
        
        # Ask if user wants to run migration now
        response = input("\nDo you want to run the migration now? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            print("\nStarting migration...")
            try:
                subprocess.check_call([sys.executable, "migrate_sqlite_to_postgres.py"])
            except subprocess.CalledProcessError as e:
                print(f"Migration failed with exit code {e.returncode}")
                sys.exit(1)
    else:
        print("\n" + "=" * 40)
        print("Please fix the issues above before running the migration.")
        sys.exit(1)

if __name__ == "__main__":
    main()
